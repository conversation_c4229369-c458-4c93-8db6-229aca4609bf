{"name": "log-viewer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "vercel-build": "vite build", "analyze": "vite build --mode analyze"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@eslint/js": "^8.56.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.15", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "globals": "^13.24.0", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "terser": "^5.39.2", "vite": "^5.1.4"}}